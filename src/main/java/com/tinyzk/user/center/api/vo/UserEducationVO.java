package com.tinyzk.user.center.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户教育经历视图对象
 */
@Data
@Schema(description = "用户教育经历视图对象")
public class UserEducationVO {

    @Schema(description = "教育经历ID")
    private Long eduId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "学校名称")
    private String schoolName;

    @Schema(description = "学位")
    private String degree;

    @Schema(description = "学位等级(1-本科, 2-硕士, 3-博士, 4-高中, 5-其他)")
    private Integer degreeLevel;

    @Schema(description = "专业")
    private String major;

    @Schema(description = "第二专业")
    private String secondaryMajor;

    @Schema(description = "专业方向")
    private String majorArea;

    @Schema(description = "专业GPA")
    private Float majorGpa;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "入学日期")
    private LocalDate startDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "毕业/离校日期(NULL表示在读)")
    private LocalDate endDate;

    @Schema(description = "描述/在校经历/荣誉等")
    private String description;

    @Schema(description = "社团经历")
    private String clubExperience;

    @Schema(description = "可见性")
    private String visibility;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
