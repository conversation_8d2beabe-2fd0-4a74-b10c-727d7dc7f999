package com.tinyzk.user.center.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

/**
 * 实名认证请求 DTO
 */
@Data
@Schema(description = "实名认证请求")
public class RealNameAuthDTO {
    
    @NotNull(message = "用户ID不能为空")
    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long userId;
    
    @NotBlank(message = "真实姓名不能为空")
    @Schema(description = "真实姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String realName;
    
    @NotBlank(message = "身份证号码不能为空")
    @Pattern(regexp = "(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)", message = "身份证号码格式不正确")
    @Schema(description = "身份证号码", requiredMode = Schema.RequiredMode.REQUIRED, example = "440106199001011234")
    private String idNumber;
}
