package com.tinyzk.user.center.api.client;

import com.tinyzk.user.center.api.common.Result;
import com.tinyzk.user.center.api.dto.CreateProjectDTO;
import com.tinyzk.user.center.api.dto.UpdateProjectDTO;
import com.tinyzk.user.center.api.vo.UserProjectVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户项目经历服务客户端
 */
@FeignClient(name = "user-center", path = "/api/v1/me/projects")
public interface UserProjectClient {
    
    /**
     * 获取当前用户项目经历列表
     *
     * @return 项目经历列表
     */
    @GetMapping
    @Operation(summary = "获取当前用户项目经历列表", description = "获取当前登录用户的所有项目经历列表")
    Result<List<UserProjectVO>> getCurrentUserProjects();
    
    /**
     * 获取当前用户特定项目经历
     *
     * @param projectId 项目经历ID
     * @return 项目经历详情
     */
    @GetMapping("/{projectId}")
    @Operation(summary = "获取当前用户特定项目经历", description = "获取当前登录用户的某条特定项目经历")
    Result<UserProjectVO> getCurrentUserProject(
            @Parameter(description = "项目经历ID", required = true)
            @PathVariable Long projectId);
    
    /**
     * 创建当前用户项目经历
     *
     * @param createProjectDTO 创建项目经历信息
     * @return 创建后的项目经历
     */
    @PostMapping
    @Operation(summary = "创建当前用户项目经历", description = "为当前登录用户添加一条新的项目经历")
    Result<UserProjectVO> createCurrentUserProject(
            @Parameter(description = "创建的项目经历信息", required = true)
            @Validated @RequestBody CreateProjectDTO createProjectDTO);
    
    /**
     * 更新当前用户项目经历
     *
     * @param projectId       项目经历ID
     * @param updateProjectDTO 更新信息
     * @return 更新后的项目经历
     */
    @PutMapping("/{projectId}")
    @Operation(summary = "更新当前用户项目经历", description = "更新当前登录用户的某条特定项目经历")
    Result<UserProjectVO> updateCurrentUserProject(
            @Parameter(description = "项目经历ID", required = true)
            @PathVariable Long projectId,
            @Parameter(description = "更新的项目经历信息", required = true)
            @Validated @RequestBody UpdateProjectDTO updateProjectDTO);
    
    /**
     * 删除当前用户项目经历
     *
     * @param projectId 项目经历ID
     * @return 删除结果
     */
    @DeleteMapping("/{projectId}")
    @Operation(summary = "删除当前用户项目经历", description = "删除当前登录用户的某条特定项目经历")
    Result<Void> deleteCurrentUserProject(
            @Parameter(description = "项目经历ID", required = true)
            @PathVariable Long projectId);
}
