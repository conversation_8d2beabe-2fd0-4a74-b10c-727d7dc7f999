package com.tinyzk.user.center.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 注册响应视图对象
 */
@Data
@Schema(description = "注册响应VO")
public class RegisterVO {
    
    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;
    
    /**
     * 用户昵称
     */
    @Schema(description = "昵称")
    private String nickname;
    
    /**
     * 头像URL
     */
    @Schema(description = "头像URL")
    private String avatarUrl;

    /**
     * JWT Token
     */
    @Schema(description = "JWT Token")
    private String token;
}
