package com.tinyzk.user.center.api.dto;

import com.tinyzk.user.center.api.enums.IdentityType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 登录请求数据传输对象
 */
@Data
public class LoginDTO {
    
    /**
     * 身份类型
     */
    @NotNull(message = "身份类型不能为空")
    @Schema(description = "身份类型")
    private IdentityType identityType;
    
    /**
     * 身份标识（如：openid、手机号、用户名等）
     */
    @NotBlank(message = "身份标识不能为空")
    @Schema(description = "身份标识")
    private String identifier;
    
    /**
     * 凭证（如：密码、验证码等）
     * 对于第三方登录，可能不需要凭证
     */
    @Schema(description = "凭证")
    private String credential;
}
