package com.tinyzk.user.center.api.vo;

import com.tinyzk.user.center.api.enums.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户个人资料视图对象
 */
@Data
@Schema(description = "用户个人资料视图对象")
public class UserProfileVO {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像URL")
    private String avatarUrl;

    @Schema(description = "性别, 0-未知, 1-男, 2-女")
    private Integer gender;

    @Schema(description = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate birthday;

    @Schema(description = "国籍")
    private String nationality;
    
    @Schema(description = "民族")
    private String ethnicity;
    
    @Schema(description = "特殊身份")
    private SpecialStatus specialStatus;
    
    @Schema(description = "政治面貌")
    private PoliticalStatus politicalStatus;
    
    @Schema(description = "婚姻状况")
    private MaritalStatus maritalStatus;
    
    @Schema(description = "生育情况")
    private FertilityStatus fertilityStatus;
    
    @Schema(description = "健康状况")
    private HealthStatus healthStatus;
    
    @Schema(description = "地区编码")
    private String regionCode;
    
    @Schema(description = "地区名称")
    private String regionName;
    
    @Schema(description = "详细地址")
    private String address;
    
    @Schema(description = "个人简介")
    private String bio;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
}
