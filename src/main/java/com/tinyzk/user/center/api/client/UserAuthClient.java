package com.tinyzk.user.center.api.client;

import com.tinyzk.user.center.api.common.Result;
import com.tinyzk.user.center.api.dto.ExternalLoginDTO;
import com.tinyzk.user.center.api.dto.LoginDTO;
import com.tinyzk.user.center.api.dto.RegisterDTO;
import com.tinyzk.user.center.api.vo.ExternalLoginVO;
import com.tinyzk.user.center.api.vo.LoginVO;
import com.tinyzk.user.center.api.vo.RegisterVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 用户认证服务客户端
 */
@FeignClient(name = "user-center", path = "/api/v1/auth")
public interface UserAuthClient {
    
    /**
     * 用户登录
     *
     * @param loginDTO 登录信息
     * @return 登录结果
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户通过不同方式（微信、支付宝、抖音小程序等）登录系统")
    Result<LoginVO> login(
            @Parameter(description = "登录信息", required = true)
            @RequestBody @Validated LoginDTO loginDTO);
    
    /**
     * 用户注册
     *
     * @param registerDTO 注册信息
     * @return 注册结果
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "用户主动注册（非首次登录的隐式注册）")
    Result<RegisterVO> register(
            @Parameter(description = "注册信息", required = true)
            @RequestBody @Validated RegisterDTO registerDTO);
    
    /**
     * 第三方业务系统登录
     *
     * @param externalLoginDTO 第三方登录信息
     * @return 登录结果
     */
    @PostMapping("/external-login")
    @Operation(summary = "第三方业务系统登录", 
               description = "根据第三方业务用户ID和客户端ID进行登录，仅用于第三方业务系统")
    Result<ExternalLoginVO> externalLogin(
            @Parameter(description = "第三方登录信息", required = true)
            @RequestBody @Validated ExternalLoginDTO externalLoginDTO);
}
