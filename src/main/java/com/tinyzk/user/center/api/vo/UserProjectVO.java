package com.tinyzk.user.center.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户项目经历视图对象
 */
@Data
@Schema(description = "用户项目经历视图对象")
public class UserProjectVO {

    @Schema(description = "项目经历ID")
    private Long projectId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "角色/职责")
    private String role;

    @Schema(description = "项目开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @Schema(description = "项目结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @Schema(description = "项目描述")
    private String description;

    @Schema(description = "项目链接")
    private String projectUrl;

    @Schema(description = "关联组织/公司")
    private String associatedOrganization;

    @Schema(description = "可见性")
    private String visibility;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;
}
