package com.tinyzk.user.center.api.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 实名认证响应 VO
 */
@Data
@Schema(description = "实名认证响应")
public class RealNameAuthVO {
    
    @Schema(description = "用户ID")
    private Long userId;
    
    @Schema(description = "真实姓名（脱敏）")
    private String realName;
    
    @Schema(description = "身份证号（脱敏）")
    private String idNumber;
    
    @Schema(description = "认证状态")
    private Integer authStatus;
    
    @Schema(description = "认证时间")
    private LocalDateTime authTime;
    
    @Schema(description = "是否发生账户合并")
    private Boolean accountMerged;
    
    @Schema(description = "合并后的主用户ID")
    private Long mergedToUserId;
}
