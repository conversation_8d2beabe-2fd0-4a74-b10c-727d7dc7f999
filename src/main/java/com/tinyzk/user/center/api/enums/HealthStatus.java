package com.tinyzk.user.center.api.enums;

/**
 * 健康状况枚举
 */
public enum HealthStatus {
    /**
     * 1-健康,2-良好,3-一般,4-较差
     */
    HEALTHY(1, "健康"),
    GOOD(2, "良好"),
    AVERAGE(3, "一般"),
    POOR(4, "较差");

    private final Integer value;
    private final String description;

    HealthStatus(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static HealthStatus getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (HealthStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
}
