package com.tinyzk.user.center.api.client;

import com.tinyzk.user.center.api.common.Result;
import com.tinyzk.user.center.api.dto.CreateEducationDTO;
import com.tinyzk.user.center.api.dto.UpdateEducationDTO;
import com.tinyzk.user.center.api.vo.UserEducationVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户教育经历服务客户端
 */
@FeignClient(name = "user-center", path = "/api/v1/me/educations")
public interface UserEducationClient {
    
    /**
     * 获取当前用户教育经历列表
     *
     * @return 教育经历列表
     */
    @GetMapping
    @Operation(summary = "获取当前用户教育经历列表", description = "获取当前登录用户的所有教育经历列表")
    Result<List<UserEducationVO>> getCurrentUserEducations();
    
    /**
     * 获取当前用户特定教育经历
     *
     * @param educationId 教育经历ID
     * @return 教育经历详情
     */
    @GetMapping("/{educationId}")
    @Operation(summary = "获取当前用户特定教育经历", description = "获取当前登录用户的某条特定教育经历")
    Result<UserEducationVO> getCurrentUserEducation(
            @Parameter(description = "教育经历ID", required = true)
            @PathVariable Long educationId);
    
    /**
     * 创建当前用户教育经历
     *
     * @param createEducationDTO 创建教育经历信息
     * @return 创建后的教育经历
     */
    @PostMapping
    @Operation(summary = "创建当前用户教育经历", description = "为当前登录用户添加一条新的教育经历")
    Result<UserEducationVO> createCurrentUserEducation(
            @Parameter(description = "创建的教育经历信息", required = true)
            @Validated @RequestBody CreateEducationDTO createEducationDTO);
    
    /**
     * 更新当前用户教育经历
     *
     * @param educationId        教育经历ID
     * @param updateEducationDTO 更新信息
     * @return 更新后的教育经历
     */
    @PutMapping("/{educationId}")
    @Operation(summary = "更新当前用户教育经历", description = "更新当前登录用户的某条特定教育经历")
    Result<UserEducationVO> updateCurrentUserEducation(
            @Parameter(description = "教育经历ID", required = true)
            @PathVariable Long educationId,
            @Parameter(description = "更新的教育经历信息", required = true)
            @Validated @RequestBody UpdateEducationDTO updateEducationDTO);
    
    /**
     * 删除当前用户教育经历
     *
     * @param educationId 教育经历ID
     * @return 删除结果
     */
    @DeleteMapping("/{educationId}")
    @Operation(summary = "删除当前用户教育经历", description = "删除当前登录用户的某条特定教育经历")
    Result<Void> deleteCurrentUserEducation(
            @Parameter(description = "教育经历ID", required = true)
            @PathVariable Long educationId);
}
