package com.tinyzk.user.center.api.client;

import com.tinyzk.user.center.api.common.Result;
import com.tinyzk.user.center.api.dto.CreateProfileDTO;
import com.tinyzk.user.center.api.dto.UpdateProfileDTO;
import com.tinyzk.user.center.api.vo.UserProfileVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户个人资料服务客户端
 */
@FeignClient(name = "user-center", path = "/api/v1/me")
public interface UserProfileClient {
    
    /**
     * 查询当前用户个人资料
     *
     * @return 用户个人资料
     */
    @GetMapping("/profile")
    @Operation(summary = "查询当前用户个人资料", description = "获取当前登录用户的个人资料信息")
    Result<UserProfileVO> getCurrentUserProfile();
    
    /**
     * 创建当前用户个人资料
     *
     * @param createProfileDTO 创建资料信息
     * @return 创建后的用户个人资料
     */
    @PostMapping("/profile")
    @Operation(summary = "创建当前用户个人资料", description = "为当前登录用户创建个人资料信息(幂等操作)")
    Result<UserProfileVO> createCurrentUserProfile(
            @Parameter(description = "创建的个人资料信息", required = true)
            @Validated @RequestBody CreateProfileDTO createProfileDTO);
    
    /**
     * 更新当前用户个人资料
     *
     * @param updateProfileDTO 更新的个人资料信息
     * @return 更新后的用户个人资料
     */
    @PutMapping("/profile")
    @Operation(summary = "更新当前用户个人资料", description = "更新当前登录用户的个人资料信息")
    Result<UserProfileVO> updateCurrentUserProfile(
            @Parameter(description = "更新的个人资料信息", required = true)
            @Validated @RequestBody UpdateProfileDTO updateProfileDTO);
    
    /**
     * 删除当前用户个人资料
     *
     * @return 删除结果
     */
    @DeleteMapping("/profile")
    @Operation(summary = "删除当前用户个人资料", description = "删除当前登录用户的个人资料信息")
    Result<Void> deleteCurrentUserProfile();
}
