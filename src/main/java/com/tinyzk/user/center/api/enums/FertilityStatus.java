package com.tinyzk.user.center.api.enums;

/**
 * 生育情况枚举
 */
public enum FertilityStatus {
    /**
     * 1-未育,2-已育,3-已育一孩,4-已育两孩及以上
     */
    UNBORN(1, "未育"),
    BORN(2, "已育"),
    BORN_ONE_CHILD(3, "已育一孩"),
    BORN_TWO_CHILDREN_OR_MORE(4, "已育两孩及以上");

    private final Integer value;
    private final String description;

    FertilityStatus(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static FertilityStatus getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (FertilityStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
}
