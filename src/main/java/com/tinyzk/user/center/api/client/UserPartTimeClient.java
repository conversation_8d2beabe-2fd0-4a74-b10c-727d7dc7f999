package com.tinyzk.user.center.api.client;

import com.tinyzk.user.center.api.common.Result;
import com.tinyzk.user.center.api.dto.CreatePartTimeDTO;
import com.tinyzk.user.center.api.dto.UpdatePartTimeDTO;
import com.tinyzk.user.center.api.vo.UserPartTimeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户兼职经历服务客户端
 */
@FeignClient(name = "user-center", path = "/api/v1/me/part_times")
public interface UserPartTimeClient {
    
    /**
     * 获取当前用户兼职经历列表
     *
     * @return 兼职经历列表
     */
    @GetMapping
    @Operation(summary = "获取当前用户兼职经历列表", description = "获取当前登录用户的所有兼职经历列表")
    Result<List<UserPartTimeVO>> getCurrentUserPartTimes();
    
    /**
     * 获取当前用户特定兼职经历
     *
     * @param partTimeId 兼职经历ID
     * @return 兼职经历详情
     */
    @GetMapping("/{partTimeId}")
    @Operation(summary = "获取当前用户特定兼职经历", description = "获取当前登录用户的某条特定兼职经历")
    Result<UserPartTimeVO> getCurrentUserPartTime(
            @Parameter(description = "兼职经历ID", required = true)
            @PathVariable Long partTimeId);
    
    /**
     * 创建当前用户兼职经历
     *
     * @param createPartTimeDTO 创建的兼职经历信息
     * @return 创建后的兼职经历
     */
    @PostMapping
    @Operation(summary = "创建当前用户兼职经历", description = "为当前登录用户创建兼职经历信息")
    Result<UserPartTimeVO> createCurrentUserPartTime(
            @Parameter(description = "创建的兼职经历信息", required = true)
            @Validated @RequestBody CreatePartTimeDTO createPartTimeDTO);
    
    /**
     * 更新当前用户兼职经历
     *
     * @param partTimeId 兼职经历ID
     * @param updatePartTimeDTO 更新的兼职经历信息
     * @return 更新后的兼职经历
     */
    @PutMapping("/{partTimeId}")
    @Operation(summary = "更新当前用户兼职经历", description = "更新当前登录用户的兼职经历信息")
    Result<UserPartTimeVO> updateCurrentUserPartTime(
            @Parameter(description = "兼职经历ID", required = true)
            @PathVariable Long partTimeId,
            @Parameter(description = "更新的兼职经历信息", required = true)
            @Validated @RequestBody UpdatePartTimeDTO updatePartTimeDTO);
    
    /**
     * 删除当前用户兼职经历
     *
     * @param partTimeId 兼职经历ID
     * @return 删除结果
     */
    @DeleteMapping("/{partTimeId}")
    @Operation(summary = "删除当前用户兼职经历", description = "删除当前登录用户的兼职经历信息")
    Result<Void> deleteCurrentUserPartTime(
            @Parameter(description = "兼职经历ID", required = true)
            @PathVariable Long partTimeId);
}
