package com.tinyzk.user.center.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 第三方业务系统登录请求数据传输对象
 */
@Data
@Schema(description = "第三方业务系统登录请求")
public class ExternalLoginDTO {
    
    /**
     * 第三方业务用户ID
     */
    @NotBlank(message = "第三方业务用户ID不能为空")
    @Schema(description = "第三方业务用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private String externalUserId;
}
