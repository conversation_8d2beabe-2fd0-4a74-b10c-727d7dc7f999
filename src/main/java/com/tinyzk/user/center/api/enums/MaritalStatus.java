package com.tinyzk.user.center.api.enums;

/**
 * 婚姻状况枚举
 */
public enum MaritalStatus {
    /**
     * 1-未婚,2-已婚,3-离异,4-丧偶
     */
    UNMARRIED(1, "未婚"),
    MARRIED(2, "已婚"),
    DIVORCED(3, "离异"),
    WIDOWER(4, "丧偶");

    private final Integer value;
    private final String description;

    MaritalStatus(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static MaritalStatus getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (MaritalStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
}
