package com.tinyzk.user.center.api.client;

import com.tinyzk.user.center.api.common.Result;
import com.tinyzk.user.center.api.dto.RealNameAuthDTO;
import com.tinyzk.user.center.api.vo.RealNameAuthVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.validation.Valid;

/**
 * 用户实名认证服务客户端
 */
@FeignClient(name = "user-center", path = "/api/v1/auth/real-name")
public interface UserRealNameAuthClient {
    
    /**
     * 提交实名认证信息
     *
     * @param realNameAuthDTO 实名认证信息 DTO
     * @return 认证结果
     */
    @PostMapping
    @Operation(summary = "提交实名认证", description = "用户提交真实姓名和身份证号进行认证")
    Result<RealNameAuthVO> submitRealNameAuth(
            @Valid @RequestBody RealNameAuthDTO realNameAuthDTO);
    
    /**
     * 查询用户实名认证信息
     *
     * @param userId 用户ID
     * @return 认证信息
     */
    @GetMapping("/{userId}")
    @Operation(summary = "查询实名认证信息", description = "根据用户ID查询其最新的实名认证状态和信息")
    Result<RealNameAuthVO> getRealNameAuth(
            @Parameter(description = "用户ID", required = true, example = "1")
            @PathVariable Long userId);
}
