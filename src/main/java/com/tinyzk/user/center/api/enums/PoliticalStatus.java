package com.tinyzk.user.center.api.enums;

/**
 * 政治面貌枚举
 */
public enum PoliticalStatus {
    /**
     *1-中共党员, 2-中共预备党员, 3-共青团员, 4-群众
     */
    CPC_MEMBER(1, "中共党员"),
    CPC_PREPARED_MEMBER(2, "中共预备党员"),
    CPTU_MEMBER(3, "共青团员"),
    PUBLIC(4, "群众");

    private final Integer value;
    private final String description;
    
    PoliticalStatus(Integer value, String description) {
        this.value = value;
        this.description = description;
    }

    public Integer getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static PoliticalStatus getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (PoliticalStatus status : values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
}
