package com.tinyzk.user.center.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Past;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;

/**
 * 更新用户项目经历数据传输对象
 */
@Data
@Schema(description = "更新用户项目经历请求")
public class UpdateProjectDTO {

    @Size(max = 255, message = "项目名称长度不能超过255个字符")
    @Schema(description = "项目名称", example = "智能用户中心开发")
    private String projectName;

    @Size(max = 100, message = "项目角色长度不能超过100个字符")
    @Schema(description = "用户在项目中的角色/职责", example = "后端开发工程师")
    private String role;

    @Past(message = "项目开始日期必须是过去的日期")
    @Schema(description = "项目开始日期", example = "2022-01-01")
    private LocalDate startDate;

    @Schema(description = "项目结束日期(NULL表示进行中)", example = "2023-01-01")
    private LocalDate endDate;

    @Size(max = 1000, message = "项目描述长度不能超过1000个字符")
    @Schema(description = "项目描述", example = "负责用户中心后端架构设计和核心功能开发")
    private String description;

    @Size(max = 255, message = "项目链接长度不能超过255个字符")
    @Schema(description = "项目链接", example = "https://github.com/example/project")
    private String projectUrl;

    @Size(max = 255, message = "关联组织/公司长度不能超过255个字符")
    @Schema(description = "关联组织/公司", example = "某科技有限公司")
    private String associatedOrganization;

    @Schema(description = "可见性(PUBLIC-公开, FRIENDS-好友可见, PRIVATE-私密)", example = "PUBLIC")
    private String visibility;
}
