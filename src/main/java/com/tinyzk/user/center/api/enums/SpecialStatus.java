package com.tinyzk.user.center.api.enums;

/**
 * 特殊身份枚举
 */
public enum SpecialStatus {
    /**
     * 1-军人,2-警察,3-医生,4-教师,5-残疾人,6-其他
     */
    SOLDIER(1, "军人"),
    POLICE(2, "警察"),
    DOCTOR(3, "医生"),
    TEACHER(4, "教师"),
    DISABLED(5, "残疾人"),
    OTHER(6, "其他");

    private final int value;
    private final String description;

    SpecialStatus(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public Integer getValue() {
        return this.value;
    }

    public String getDescription() {
        return this.description;
    }

    /**
     * 根据值获取对应的枚举
     */
    public static SpecialStatus getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (SpecialStatus status : values()) {
            if (status.value == value) {
                return status;
            }
        }
        return null;
    }
}
