package com.tinyzk.user.center.api.dto;

import com.tinyzk.user.center.api.enums.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * 更新用户个人资料数据传输对象
 */
@Data
@Schema(description = "更新用户个人资料数据传输对象")
public class UpdateProfileDTO {

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像URL")
    private String avatarUrl;

    @Schema(description = "性别, 0-未知, 1-男, 2-女")
    private Integer gender;

    @Schema(description = "生日")
    private LocalDate birthday;

    @Schema(description = "国籍")
    private String nationality;
    
    @Schema(description = "民族")
    private String ethnicity;
    
    @Schema(description = "特殊身份")
    private SpecialStatus specialStatus;
    
    @Schema(description = "政治面貌")
    private PoliticalStatus politicalStatus;
    
    @Schema(description = "婚姻状况")
    private MaritalStatus maritalStatus;
    
    @Schema(description = "生育情况")
    private FertilityStatus fertilityStatus;
    
    @Schema(description = "健康状况")
    private HealthStatus healthStatus;
    
    @Schema(description = "地区编码")
    private String regionCode;
    
    @Schema(description = "地区名称")
    private String regionName;
    
    @Schema(description = "详细地址")
    private String address;
    
    @Schema(description = "个人简介")
    private String bio;

    // 注意：这里只包含允许用户修改的字段
    // 不允许用户直接修改 userId, createdAt, updatedAt, deletedAt 等字段
}
