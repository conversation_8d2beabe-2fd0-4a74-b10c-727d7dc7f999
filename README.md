# 用户中心API模块

这是用户中心的API模块，提供OpenFeign客户端接口，供其他微服务调用。

## 功能特性

- 🚀 轻量级设计，最小化依赖
- 🔌 开箱即用的OpenFeign客户端
- 📝 完整的API文档和类型定义
- 🛡️ 内置请求头传递和安全配置
- 🎯 标准化的响应格式

## 快速开始

### 1. 添加依赖

在您的微服务项目中添加以下依赖：

```xml
<dependency>
    <groupId>com.tinyzk</groupId>
    <artifactId>user-center-api</artifactId>
    <version>0.0.1-SNAPSHOT</version>
</dependency>
```

### 2. 启用Feign客户端

在您的Spring Boot应用主类上添加注解：

```java
@SpringBootApplication
@EnableFeignClients(basePackages = "com.tinyzk.user.center.api.client")
public class YourApplication {
    public static void main(String[] args) {
        SpringApplication.run(YourApplication.class, args);
    }
}
```

### 3. 配置服务发现

在`application.yml`中配置用户中心服务：

```yaml
spring:
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        
# Feign配置
feign:
  client:
    config:
      user-center:
        connect-timeout: 5000
        read-timeout: 10000
        logger-level: basic
```

### 4. 使用客户端

```java
@RestController
@RequiredArgsConstructor
public class YourController {

    private final UserAuthClient userAuthClient;
    private final UserRealNameAuthClient realNameAuthClient;
    private final UserProfileClient userProfileClient;
    private final UserPartTimeClient userPartTimeClient;
    private final UserEducationClient userEducationClient;
    private final UserProjectClient userProjectClient;

    // 用户认证相关
    @PostMapping("/login")
    public Result<LoginVO> login(@RequestBody LoginDTO loginDTO) {
        return userAuthClient.login(loginDTO);
    }

    @GetMapping("/user/{userId}/real-name-auth")
    public Result<RealNameAuthVO> getRealNameAuth(@PathVariable Long userId) {
        return realNameAuthClient.getRealNameAuth(userId);
    }

    // 用户资料相关
    @GetMapping("/profile")
    public Result<UserProfileVO> getCurrentUserProfile() {
        return userProfileClient.getCurrentUserProfile();
    }

    @PostMapping("/profile")
    public Result<UserProfileVO> createProfile(@RequestBody CreateProfileDTO dto) {
        return userProfileClient.createCurrentUserProfile(dto);
    }

    // 兼职经历相关
    @GetMapping("/part-times")
    public Result<List<UserPartTimeVO>> getPartTimes() {
        return userPartTimeClient.getCurrentUserPartTimes();
    }

    @PostMapping("/part-times")
    public Result<UserPartTimeVO> createPartTime(@RequestBody CreatePartTimeDTO dto) {
        return userPartTimeClient.createCurrentUserPartTime(dto);
    }

    // 教育经历相关
    @GetMapping("/educations")
    public Result<List<UserEducationVO>> getEducations() {
        return userEducationClient.getCurrentUserEducations();
    }

    // 项目经历相关
    @GetMapping("/projects")
    public Result<List<UserProjectVO>> getProjects() {
        return userProjectClient.getCurrentUserProjects();
    }
}
```

## API接口

### 用户认证接口 (UserAuthClient)

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/v1/auth/login` | 用户登录 |
| POST | `/api/v1/auth/register` | 用户注册 |
| POST | `/api/v1/auth/external-login` | 第三方系统登录 |

### 实名认证接口 (UserRealNameAuthClient)

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/v1/auth/real-name` | 提交实名认证 |
| GET | `/api/v1/auth/real-name/{userId}` | 查询实名认证信息 |

### 用户个人资料接口 (UserProfileClient)

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/v1/me/profile` | 查询当前用户个人资料 |
| POST | `/api/v1/me/profile` | 创建当前用户个人资料 |
| PUT | `/api/v1/me/profile` | 更新当前用户个人资料 |
| DELETE | `/api/v1/me/profile` | 删除当前用户个人资料 |

### 用户兼职经历接口 (UserPartTimeClient)

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/v1/me/part_times` | 获取当前用户兼职经历列表 |
| GET | `/api/v1/me/part_times/{partTimeId}` | 获取当前用户特定兼职经历 |
| POST | `/api/v1/me/part_times` | 创建当前用户兼职经历 |
| PUT | `/api/v1/me/part_times/{partTimeId}` | 更新当前用户兼职经历 |
| DELETE | `/api/v1/me/part_times/{partTimeId}` | 删除当前用户兼职经历 |

### 用户教育经历接口 (UserEducationClient)

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/v1/me/educations` | 获取当前用户教育经历列表 |
| GET | `/api/v1/me/educations/{educationId}` | 获取当前用户特定教育经历 |
| POST | `/api/v1/me/educations` | 创建当前用户教育经历 |
| PUT | `/api/v1/me/educations/{educationId}` | 更新当前用户教育经历 |
| DELETE | `/api/v1/me/educations/{educationId}` | 删除当前用户教育经历 |

### 用户项目经历接口 (UserProjectClient)

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/v1/me/projects` | 获取当前用户项目经历列表 |
| GET | `/api/v1/me/projects/{projectId}` | 获取当前用户特定项目经历 |
| POST | `/api/v1/me/projects` | 创建当前用户项目经历 |
| PUT | `/api/v1/me/projects/{projectId}` | 更新当前用户项目经历 |
| DELETE | `/api/v1/me/projects/{projectId}` | 删除当前用户项目经历 |

## 数据传输对象

### 请求DTO

#### 用户认证相关
- `LoginDTO` - 登录请求
- `RegisterDTO` - 注册请求
- `ExternalLoginDTO` - 第三方登录请求
- `RealNameAuthDTO` - 实名认证请求

#### 用户资料相关
- `CreateProfileDTO` - 创建个人资料请求
- `UpdateProfileDTO` - 更新个人资料请求

#### 兼职经历相关
- `CreatePartTimeDTO` - 创建兼职经历请求
- `UpdatePartTimeDTO` - 更新兼职经历请求

#### 教育经历相关
- `CreateEducationDTO` - 创建教育经历请求
- `UpdateEducationDTO` - 更新教育经历请求

#### 项目经历相关
- `CreateProjectDTO` - 创建项目经历请求
- `UpdateProjectDTO` - 更新项目经历请求

### 响应VO

#### 用户认证相关
- `LoginVO` - 登录响应
- `RegisterVO` - 注册响应
- `ExternalLoginVO` - 第三方登录响应
- `RealNameAuthVO` - 实名认证响应

#### 用户资料相关
- `UserProfileVO` - 用户个人资料响应

#### 经历相关
- `UserPartTimeVO` - 用户兼职经历响应
- `UserEducationVO` - 用户教育经历响应
- `UserProjectVO` - 用户项目经历响应

### 枚举类

- `IdentityType` - 身份认证类型
- `SpecialStatus` - 特殊身份状态
- `PoliticalStatus` - 政治面貌
- `MaritalStatus` - 婚姻状况
- `FertilityStatus` - 生育情况
- `HealthStatus` - 健康状况

## 配置说明

### 自动配置

API模块提供了`UserCenterFeignConfig`配置类，自动处理：

- 请求头传递（X-Client-ID、Authorization、X-User-ID）
- 日志级别配置
- 超时设置

### 自定义配置

如需自定义配置，可以创建自己的配置类：

```java
@Configuration
public class CustomFeignConfig {
    
    @Bean
    public RequestInterceptor customRequestInterceptor() {
        return template -> {
            // 自定义请求拦截逻辑
        };
    }
}
```

## 错误处理

所有API响应都使用统一的`Result<T>`格式：

```java
{
    "code": 200,
    "message": "操作成功",
    "data": { ... }
}
```

常见错误码：
- 200: 成功
- 400: 参数错误
- 401: 未认证
- 403: 未授权
- 404: 资源不存在
- 500: 系统错误

## 版本兼容性

- Java 17+
- Spring Boot 3.2.0+
- Spring Cloud 2023.0.0+

## 注意事项

1. 确保用户中心服务已正确注册到Nacos
2. 配置正确的服务名称（默认为`user-center`）
3. 传递必要的请求头（如X-Client-ID）
4. 处理网络异常和服务降级

## 更新日志

### v0.0.1-SNAPSHOT
- 初始版本
- 提供基础的用户认证和实名认证接口
- 支持OpenFeign客户端调用
